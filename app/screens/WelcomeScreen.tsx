import { FC } from "react"
import { FlatList, Image, ImageStyle, TextStyle, View, ViewStyle } from "react-native"

import { But<PERSON> } from "@/components/Button"
import { Screen } from "@/components/Screen"
import { Text } from "@/components/Text"
import { useAuth } from "@/context/AuthContext"
import { isRTL } from "@/i18n"
import type { AppStackScreenProps } from "@/navigators/AppNavigator"
import { useAppTheme } from "@/theme/context"
import { $styles } from "@/theme/styles"
import type { ThemedStyle } from "@/theme/types"
import { useHeader } from "@/utils/useHeader"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { Header } from "@/components/Header"
import { da } from "date-fns/locale"
import { Icon } from "@/components/Icon"
import { IconV2 } from "@/components/IconV2"

interface WelcomeScreenProps extends AppStackScreenProps<"Welcome"> {}
const data = [
  {
    icon: "export",
    title: "Export",
  },
  {
    icon: "export",
    title: "Export",
  },
]
export const WelcomeScreen: FC<WelcomeScreenProps> = function WelcomeScreen(_props) {
  const { themed, theme } = useAppTheme()

  const { navigation } = _props
  const { logout } = useAuth()

  function goNext() {
    navigation.navigate("Demo", { screen: "DemoShowroom", params: {} })
  }

  return (
    <Screen contentContainerStyle={$styles.flex1}>
      <Header title="Trang chu" />
      <FlatList
        data={data}
        keyExtractor={(_, index) => index.toString()}
        numColumns={2}
        style={themed($contentStyle)}
        renderItem={({ item }) => {
          return (
            <View style={themed($container)}>
              <IconV2 icon={item.icon} size={100} />
              <Image source={require("@assets/icons/export.png")} style={$imageStyle} />
              <Text>{item.title}</Text>
            </View>
          )
        }}
      />
    </Screen>
  )
}

const $imageStyle: ImageStyle = {
  height: 100,
  width: 100,
}

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: "48%",
  marginTop: 10,
  marginHorizontal: "auto",
  justifyContent: "center",
  alignItems: "center",
  height: 200,
  borderRadius: 10,
  backgroundColor: "#fff",
  shadowColor: "#000",
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,

  elevation: 5,
})

const $contentStyle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
})

const $topContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexShrink: 1,
  flexGrow: 1,
  flexBasis: "57%",
  justifyContent: "center",
  paddingHorizontal: spacing.lg,
})

const $bottomContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexShrink: 1,
  flexGrow: 0,
  flexBasis: "43%",
  backgroundColor: colors.palette.neutral100,
  borderTopLeftRadius: 16,
  borderTopRightRadius: 16,
  paddingHorizontal: spacing.lg,
  justifyContent: "space-around",
})

const $welcomeLogo: ThemedStyle<ImageStyle> = ({ spacing }) => ({
  height: 88,
  width: "100%",
  marginBottom: spacing.xxl,
})

const $welcomeFace: ImageStyle = {
  height: 169,
  width: 269,
  position: "absolute",
  bottom: -47,
  right: -80,
  transform: [{ scaleX: isRTL ? -1 : 1 }],
}

const $welcomeHeading: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})
