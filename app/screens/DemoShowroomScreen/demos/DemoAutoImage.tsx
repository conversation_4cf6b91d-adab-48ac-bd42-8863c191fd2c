/* eslint-disable react/jsx-key, react-native/no-inline-styles */
import { Image, ImageStyle, TextStyle, View, ViewStyle } from "react-native"

import { AutoImage } from "@/components/AutoImage"
import { Text } from "@/components/Text"
import { translate } from "@/i18n/translate"
import { $styles } from "@/theme/styles"
import type { ThemedStyle } from "@/theme/types"

import { DemoDivider } from "../DemoDivider"
import { Demo } from "../DemoShowroomScreen"
import { DemoUseCase } from "../DemoUseCase"

const $imageContainer: ViewStyle = {
  alignItems: "center",
}

const $aspectRatioDescription: ThemedStyle<TextStyle> = ({ spacing }) => ({
  textAlign: "center",
  width: "100%",
  marginTop: spacing.xs,
})

const $aspectRatioWidthExampleContainer: ViewStyle = {
  justifyContent: "space-between",
}

const $aspectRatioHeightExampleContainer: ViewStyle = {
  alignItems: "stretch",
  justifyContent: "space-between",
  height: 130,
}

const $aspectRatioBox: ThemedStyle<ViewStyle & ImageStyle> = (theme) => ({
  borderRadius: 4,
  borderWidth: 3,
  borderColor: theme.colors.palette.secondary300,
  backgroundColor: theme.colors.palette.neutral800,
})

export const DemoAutoImage: Demo = {
  name: "AutoImage",
  description: "demoAutoImage:description",
  data: ({ theme, themed }) => [
    <DemoUseCase name="demoAutoImage:useCase.remoteUri.name">
      <View style={$imageContainer}>
        <AutoImage
          source={{
            uri: "https://user-images.githubusercontent.com/1775841/184508739-f90d0ce5-7219-42fd-a91f-3382d016eae0.png",
          }}
        />
      </View>
    </DemoUseCase>,

    <DemoUseCase name="demoAutoImage:useCase.base64Uri.name">
      <View style={$imageContainer}>
        <AutoImage
          source={{
            uri: `data:image/png;base64,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`,
          }}
        />
      </View>
    </DemoUseCase>,

    <DemoUseCase
      name="demoAutoImage:useCase.scaledToFitDimensions.name"
      description="demoAutoImage:useCase.scaledToFitDimensions.description"
    >
      <View style={[$styles.row, $aspectRatioWidthExampleContainer]}>
        <Text
          text="<View />"
          size="xxs"
          weight="bold"
          style={{ flexBasis: "33.3333%", color: theme.colors.palette.secondary400 }}
        />
        <Text
          text="<Image />"
          size="xxs"
          weight="bold"
          style={{
            flexBasis: "33.3333%",
            textAlign: "center",
            color: theme.colors.palette.secondary400,
          }}
        />
        <Text
          text="<AutoImage />"
          size="xxs"
          weight="bold"
          style={{
            flexBasis: "33.3333%",
            textAlign: "right",
            color: theme.colors.palette.secondary400,
          }}
        />
      </View>

      <DemoDivider size={5} />

      <View style={[$styles.row, $aspectRatioWidthExampleContainer, { height: 80 }]}>
        <View style={themed([$aspectRatioBox, { width: 60 }])} />
        <Image
          source={{
            uri: "https://user-images.githubusercontent.com/1775841/188244137-a35ab1b9-658d-4701-b1dd-7caa51173fa9.png",
          }}
          style={themed([$aspectRatioBox, { width: 60 }])}
          resizeMode="contain"
        />
        <AutoImage
          maxWidth={60}
          style={themed($aspectRatioBox)}
          source={{
            uri: "https://user-images.githubusercontent.com/1775841/188244137-a35ab1b9-658d-4701-b1dd-7caa51173fa9.png",
          }}
        />
      </View>

      <Text weight="bold" size="xs" style={themed($aspectRatioDescription)}>
        {translate("demoAutoImage:useCase.scaledToFitDimensions.heightAuto")}
      </Text>

      <DemoDivider size={40} />

      <View style={$styles.row}>
        <View style={$aspectRatioHeightExampleContainer}>
          <Text
            text="<View />"
            size="xxs"
            weight="bold"
            style={{ color: theme.colors.palette.secondary400 }}
          />
          <Text
            text="<Image />"
            size="xxs"
            weight="bold"
            style={{ color: theme.colors.palette.secondary400 }}
          />
          <Text
            text="<AutoImage />"
            size="xxs"
            weight="bold"
            style={{ color: theme.colors.palette.secondary400 }}
          />
        </View>

        <View
          style={[$aspectRatioHeightExampleContainer, { flex: 1, marginStart: theme.spacing.sm }]}
        >
          <View style={themed([$aspectRatioBox, { height: 32 }])} />
          <Image
            source={{
              uri: "https://user-images.githubusercontent.com/1775841/188244137-a35ab1b9-658d-4701-b1dd-7caa51173fa9.png",
            }}
            style={themed([$aspectRatioBox, { height: 32 }])}
            resizeMode="contain"
          />
          <AutoImage
            maxHeight={32}
            style={themed([$aspectRatioBox, { alignSelf: "center" }])}
            source={{
              uri: "https://user-images.githubusercontent.com/1775841/188244137-a35ab1b9-658d-4701-b1dd-7caa51173fa9.png",
            }}
          />
        </View>
      </View>

      <Text weight="bold" size="xs" style={themed($aspectRatioDescription)}>
        {translate("demoAutoImage:useCase.scaledToFitDimensions.widthAuto")}
      </Text>

      <DemoDivider size={40} />

      <View style={[$styles.row, $aspectRatioWidthExampleContainer]}>
        <Text
          text="<View />"
          size="xxs"
          weight="bold"
          style={{ flexBasis: "33.3333%", color: theme.colors.palette.secondary400 }}
        />
        <Text
          text="<Image />"
          size="xxs"
          weight="bold"
          style={{
            flexBasis: "33.3333%",
            textAlign: "center",
            color: theme.colors.palette.secondary400,
          }}
        />
        <Text
          text="<AutoImage />"
          size="xxs"
          weight="bold"
          style={{
            flexBasis: "33.3333%",
            textAlign: "right",
            color: theme.colors.palette.secondary400,
          }}
        />
      </View>

      <DemoDivider size={5} />

      <View style={[$styles.row, $aspectRatioWidthExampleContainer]}>
        <View style={themed([$aspectRatioBox, { width: 60, height: 60 }])} />
        <Image
          source={{
            uri: "https://user-images.githubusercontent.com/1775841/188244137-a35ab1b9-658d-4701-b1dd-7caa51173fa9.png",
          }}
          style={themed([$aspectRatioBox, { width: 60, height: 60 }])}
          resizeMode="contain"
        />
        <AutoImage
          maxWidth={60}
          maxHeight={60}
          style={themed($aspectRatioBox)}
          source={{
            uri: "https://user-images.githubusercontent.com/1775841/188244137-a35ab1b9-658d-4701-b1dd-7caa51173fa9.png",
          }}
        />
      </View>

      <Text weight="bold" size="xs" style={themed($aspectRatioDescription)}>
        {translate("demoAutoImage:useCase.scaledToFitDimensions.bothManual")}
      </Text>
    </DemoUseCase>,
  ],
}
