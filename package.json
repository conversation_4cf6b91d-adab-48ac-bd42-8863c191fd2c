{"name": "my-restaurant", "version": "0.0.1", "private": true, "main": "index.tsx", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "bundle:web": "npx expo export --platform web", "serve:web": "npx serve dist", "prebuild:clean": "npx expo prebuild --clean", "compile": "tsc --noEmit -p . --pretty", "lint": "eslint . --fix", "lint:check": "eslint .", "align-deps": "npx expo install --fix", "test": "jest", "test:watch": "jest --watch", "test:maestro": "maestro test -e MAESTRO_APP_ID=com.myrestaurant .maestro/flows", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "build:ios:sim": "eas build --profile development --platform ios --local", "build:ios:dev": "eas build --profile development:device --platform ios --local", "build:ios:preview": "eas build --profile preview --platform ios --local", "build:ios:prod": "eas build --profile production --platform ios --local", "build:android:sim": "eas build --profile development --platform android --local", "build:android:dev": "eas build --profile development:device --platform android --local", "build:android:preview": "eas build --profile preview --platform android --local", "build:android:prod": "eas build --profile production --platform android --local"}, "dependencies": {"@expo-google-fonts/space-grotesk": "^0.4.0", "@expo/metro-runtime": "~5.0.4", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "apisauce": "3.1.1", "date-fns": "^4.1.0", "expo": "^53.0.15", "expo-application": "~6.1.4", "expo-build-properties": "~0.14.6", "expo-dev-client": "~5.2.1", "expo-font": "~13.3.0", "expo-linking": "~7.1.4", "expo-localization": "~16.1.5", "expo-splash-screen": "~0.30.9", "expo-system-ui": "~5.0.9", "i18next": "^23.14.0", "intl-pluralrules": "^2.0.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.0.1", "react-native": "0.79.5", "react-native-drawer-layout": "^4.0.1", "react-native-edge-to-edge": "1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.12.7", "react-native-mmkv": "2.12.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "babel-jest": "^29.2.1", "eslint": "^8.57.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-native": "^4.1.0", "eslint-plugin-reactotron": "^0.1.2", "jest": "~29.7.0", "jest-expo": "~53.0.7", "prettier": "^3.3.3", "react-test-renderer": "19.0.0", "reactotron-core-client": "^2.9.4", "reactotron-react-js": "^3.3.11", "reactotron-react-native": "^5.0.5", "reactotron-react-native-mmkv": "^0.2.6", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "engines": {"node": ">=20.0.0"}}