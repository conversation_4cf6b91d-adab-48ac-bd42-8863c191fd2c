{"name": "MyRestaurant", "slug": "MyRestaurant", "scheme": "myrestaurant", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "automatic", "icon": "./assets/images/app-icon-all.png", "updates": {"fallbackToCacheTimeout": 0}, "newArchEnabled": false, "jsEngine": "hermes", "assetBundlePatterns": ["**/*"], "android": {"icon": "./assets/images/app-icon-android-legacy.png", "package": "com.myrestaurant", "adaptiveIcon": {"foregroundImage": "./assets/images/app-icon-android-adaptive-foreground.png", "backgroundImage": "./assets/images/app-icon-android-adaptive-background.png"}, "allowBackup": false, "edgeToEdgeEnabled": true}, "ios": {"icon": "./assets/images/app-icon-ios.png", "supportsTablet": true, "bundleIdentifier": "com.myrestaurant"}, "web": {"favicon": "./assets/images/app-icon-web-favicon.png", "bundler": "metro"}, "plugins": ["expo-localization", "expo-font", ["expo-splash-screen", {"image": "./assets/images/app-icon-android-adaptive-foreground.png", "imageWidth": 300, "resizeMode": "contain", "backgroundColor": "#191015"}], ["react-native-edge-to-edge", {"android": {"parentTheme": "Light", "enforceNavigationBarContrast": false}}]], "experiments": {"tsconfigPaths": true}, "extra": {"ignite": {"version": "11.1.0"}}}